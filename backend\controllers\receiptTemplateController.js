const Store = require("../models/Store");
const PDFDocument = require('pdfkit');
const QRCode = require('qrcode');
const fs = require('fs');
const path = require('path');

// Helper function to generate QR code
const generateQRCode = async (data, size = 100) => {
  try {
    const qrCodeDataURL = await QRCode.toDataURL(data, {
      width: size,
      margin: 1,
      color: {
        dark: '#000000',
        light: '#FFFFFF'
      }
    });
    return qrCodeDataURL;
  } catch (error) {
    console.error('QR Code generation error:', error);
    return null;
  }
};

// Helper function to get translated text
const getTranslatedText = (template, key, defaultText) => {
  if (!template.advanced?.multiLanguage?.enabled) {
    return defaultText;
  }

  const translations = template.advanced.multiLanguage.translations;
  return translations.get(key) || defaultText;
};

// Helper function to generate promotional content
const generatePromotionalContent = (template, transactionData) => {
  const promotions = [];

  if (template.advanced?.dynamicContent?.enabled) {
    // Add daily specials
    if (template.advanced.dynamicContent.dailySpecials) {
      promotions.push("Today's Special: 20% off selected items!");
    }

    // Add personalized offers based on purchase history
    if (template.advanced.dynamicContent.personalizedOffers && transactionData.totalAmount > 100) {
      promotions.push("Spend R200+ next time and get 10% off!");
    }
  }

  return promotions;
};

// Get receipt template for a store
exports.getReceiptTemplate = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Check authorization
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    const store = await Store.findById(storeId);
    if (!store) {
      return res.status(404).json({ message: "Store not found" });
    }

    res.status(200).json({
      message: "Receipt template retrieved successfully",
      template: store.receiptTemplate
    });

  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// Update receipt template for a store
exports.updateReceiptTemplate = async (req, res) => {
  try {
    const { storeId } = req.params;
    const templateData = req.body;

    // Check authorization
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    const store = await Store.findById(storeId);
    if (!store) {
      return res.status(404).json({ message: "Store not found" });
    }

    // Update the receipt template
    store.receiptTemplate = {
      ...store.receiptTemplate.toObject(),
      ...templateData,
      updatedAt: new Date()
    };

    await store.save();

    res.status(200).json({
      message: "Receipt template updated successfully",
      template: store.receiptTemplate
    });

  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// Generate receipt using custom template
exports.generateCustomReceipt = async (req, res) => {
  try {
    const { products, totalAmount, storeId, transactionId, systemId, template: customTemplate } = req.body;

    // Get store and template
    let store;
    try {
      store = await Store.findById(storeId);
    } catch (error) {
      // Database error, use mock store for preview
    }

    if (!store) {
      // For preview purposes, create a mock store
      store = {
        _id: storeId,
        name: 'Preview Store',
        receiptTemplate: {}
      };
    }

    // Use custom template if provided (for preview), otherwise use store template
    let template = customTemplate || store.receiptTemplate;

    // Ensure template has required structure
    if (!template || !template.layout) {
      template = {
        ...template,
        layout: {
          paperWidth: 80,
          margin: 5,
          lineSpacing: 1.2,
          sectionSpacing: 10
        },
        header: template?.header || {
          storeName: { show: true, text: store.name || 'Store', fontSize: 18, alignment: 'center', bold: true },
          storeAddress: { show: true, text: 'Store Address', fontSize: 12, alignment: 'center' },
          storeContact: { show: true, phone: 'Tel: (*************', email: '<EMAIL>', website: 'www.store.com', fontSize: 10, alignment: 'center' },
          logo: { show: false, path: '', width: 100, height: 50, alignment: 'center' },
          customMessage: { show: true, text: 'Thank you for choosing us!', fontSize: 12, alignment: 'center' }
        },
        body: template?.body || {
          showDateTime: true,
          dateTimeFormat: 'YYYY-MM-DD HH:mm:ss',
          showTransactionId: true,
          showCashier: false,
          cashierName: 'Self-Checkout',
          itemsTable: {
            showHeaders: true,
            showQuantity: true,
            showUnitPrice: true,
            showTotalPrice: true,
            showBarcode: false,
            fontSize: 10
          },
          pricing: {
            showSubtotal: true,
            showTax: false,
            taxRate: 15,
            showDiscount: false,
            showTotal: true,
            currency: 'R',
            fontSize: 12
          }
        },
        footer: template?.footer || {
          thankYouMessage: { show: true, text: 'Thank you for shopping with us!', fontSize: 12, alignment: 'center' },
          returnPolicy: { show: false, text: '', fontSize: 8, alignment: 'center' },
          socialMedia: { show: false, facebook: '', instagram: '', twitter: '', fontSize: 8, alignment: 'center' },
          customFooter: { show: false, text: '', fontSize: 10, alignment: 'center' }
        }
      };
    }

    const transactionData = { products, totalAmount, transactionId, systemId };

    // Ensure receipts directory exists
    const receiptsDir = path.join(__dirname, '..', 'receipts');
    if (!fs.existsSync(receiptsDir)) {
      fs.mkdirSync(receiptsDir, { recursive: true });
    }

    // Create PDF document with custom template
    const doc = new PDFDocument({
      size: [template.layout.paperWidth * 2.83, 600], // Convert mm to points, fixed height for receipt
      margin: template.layout.margin * 2.83
    });

    const receiptPath = path.join(receiptsDir, `receipt_${Date.now()}.pdf`);
    doc.pipe(fs.createWriteStream(receiptPath));

    let yPosition = template.layout.margin * 2.83;

    // Header Section
    if (template.header.logo.show && template.header.logo.path) {
      try {
        const logoPath = path.join(__dirname, '..', template.header.logo.path);
        if (fs.existsSync(logoPath)) {
          doc.image(logoPath, {
            width: template.header.logo.width,
            height: template.header.logo.height,
            align: template.header.logo.alignment
          });
          yPosition += template.header.logo.height + template.layout.sectionSpacing;
        }
      } catch (error) {
        console.log('Logo loading error:', error);
      }
    }

    // Store Name
    if (template.header.storeName.show) {
      const storeName = template.header.storeName.text || store.name;
      doc.fontSize(template.header.storeName.fontSize)
         .font(template.header.storeName.bold ? 'Helvetica-Bold' : 'Helvetica')
         .text(storeName, {
           align: template.header.storeName.alignment
         });
      yPosition += template.header.storeName.fontSize + 5;
    }

    // Store Address
    if (template.header.storeAddress.show && template.header.storeAddress.text) {
      doc.fontSize(template.header.storeAddress.fontSize)
         .font('Helvetica')
         .text(template.header.storeAddress.text, {
           align: template.header.storeAddress.alignment
         });
      yPosition += template.header.storeAddress.fontSize + 5;
    }

    // Store Contact
    if (template.header.storeContact.show) {
      const contactInfo = [];
      if (template.header.storeContact.phone) contactInfo.push(`Tel: ${template.header.storeContact.phone}`);
      if (template.header.storeContact.email) contactInfo.push(`Email: ${template.header.storeContact.email}`);
      if (template.header.storeContact.website) contactInfo.push(`Web: ${template.header.storeContact.website}`);
      
      if (contactInfo.length > 0) {
        doc.fontSize(template.header.storeContact.fontSize)
           .font('Helvetica')
           .text(contactInfo.join(' | '), {
             align: template.header.storeContact.alignment
           });
        yPosition += template.header.storeContact.fontSize + 5;
      }
    }

    // Custom Header Message
    if (template.header.customMessage.show && template.header.customMessage.text) {
      doc.fontSize(template.header.customMessage.fontSize)
         .font('Helvetica')
         .text(template.header.customMessage.text, {
           align: template.header.customMessage.alignment
         });
      yPosition += template.header.customMessage.fontSize + 5;
    }

    // Separator line
    yPosition += 10;
    doc.moveTo(template.layout.margin * 2.83, yPosition)
       .lineTo(template.layout.paperWidth * 2.83 - template.layout.margin * 2.83, yPosition)
       .stroke();
    yPosition += 15;

    // Body Section
    const currentDate = new Date();
    
    // Date and Time
    if (template.body.showDateTime) {
      const dateTimeText = `Date: ${currentDate.toLocaleString()}`;
      doc.fontSize(10)
         .font('Helvetica')
         .text(dateTimeText);
      yPosition += 15;
    }

    // Transaction ID
    if (template.body.showTransactionId && transactionId) {
      doc.fontSize(10)
         .font('Helvetica')
         .text(`Transaction ID: ${transactionId}`);
      yPosition += 15;
    }

    // Cashier
    if (template.body.showCashier) {
      doc.fontSize(10)
         .font('Helvetica')
         .text(`Cashier: ${template.body.cashierName}`);
      yPosition += 15;
    }

    yPosition += 10;

    // Items Table
    if (template.body.itemsTable.showHeaders) {
      doc.fontSize(template.body.itemsTable.fontSize)
         .font('Helvetica-Bold');
      
      let headerText = 'Item';
      if (template.body.itemsTable.showQuantity) headerText += '\t\tQty';
      if (template.body.itemsTable.showUnitPrice) headerText += '\t\tPrice';
      if (template.body.itemsTable.showTotalPrice) headerText += '\t\tTotal';
      
      doc.text(headerText);
      yPosition += template.body.itemsTable.fontSize + 5;
      
      // Header separator
      doc.moveTo(template.layout.margin * 2.83, yPosition)
         .lineTo(template.layout.paperWidth * 2.83 - template.layout.margin * 2.83, yPosition)
         .stroke();
      yPosition += 10;
    }

    // Items
    let subtotal = 0;
    products.forEach(product => {
      const quantity = product.quantity || 1;
      const unitPrice = product.price || 0;
      const itemTotal = quantity * unitPrice;
      subtotal += itemTotal;

      doc.fontSize(template.body.itemsTable.fontSize)
         .font('Helvetica');

      let itemText = product.name;
      if (template.body.itemsTable.showQuantity) itemText += `\t\t${quantity}`;
      if (template.body.itemsTable.showUnitPrice) itemText += `\t\t${template.body.pricing.currency}${unitPrice.toFixed(2)}`;
      if (template.body.itemsTable.showTotalPrice) itemText += `\t\t${template.body.pricing.currency}${itemTotal.toFixed(2)}`;

      doc.text(itemText);
      yPosition += template.body.itemsTable.fontSize + 3;

      // Show barcode if enabled
      if (template.body.itemsTable.showBarcode && product.barcode) {
        doc.fontSize(8)
           .font('Helvetica')
           .text(`  Barcode: ${product.barcode}`);
        yPosition += 10;
      }
    });

    yPosition += 10;

    // Pricing Section
    doc.fontSize(template.body.pricing.fontSize)
       .font('Helvetica');

    // Subtotal
    if (template.body.pricing.showSubtotal) {
      doc.text(`Subtotal: ${template.body.pricing.currency}${subtotal.toFixed(2)}`, {
        align: 'right'
      });
      yPosition += template.body.pricing.fontSize + 3;
    }

    // Tax
    if (template.body.pricing.showTax) {
      const taxAmount = subtotal * (template.body.pricing.taxRate / 100);
      doc.text(`Tax (${template.body.pricing.taxRate}%): ${template.body.pricing.currency}${taxAmount.toFixed(2)}`, {
        align: 'right'
      });
      yPosition += template.body.pricing.fontSize + 3;
    }

    // Discount (if applicable)
    if (template.body.pricing.showDiscount) {
      const discount = subtotal - totalAmount;
      if (discount > 0) {
        doc.text(`Discount: -${template.body.pricing.currency}${discount.toFixed(2)}`, {
          align: 'right'
        });
        yPosition += template.body.pricing.fontSize + 3;
      }
    }

    // Total
    if (template.body.pricing.showTotal) {
      doc.fontSize(template.body.pricing.fontSize + 2)
         .font('Helvetica-Bold')
         .text(`TOTAL: ${template.body.pricing.currency}${totalAmount.toFixed(2)}`, {
           align: 'right'
         });
      yPosition += template.body.pricing.fontSize + 10;
    }

    // Footer Section
    yPosition += template.layout.sectionSpacing;

    // Thank you message
    if (template.footer.thankYouMessage.show) {
      doc.fontSize(template.footer.thankYouMessage.fontSize)
         .font('Helvetica-Bold')
         .text(template.footer.thankYouMessage.text, {
           align: template.footer.thankYouMessage.alignment
         });
      yPosition += template.footer.thankYouMessage.fontSize + 10;
    }

    // Return policy
    if (template.footer.returnPolicy.show && template.footer.returnPolicy.text) {
      doc.fontSize(template.footer.returnPolicy.fontSize)
         .font('Helvetica')
         .text(template.footer.returnPolicy.text, {
           align: template.footer.returnPolicy.alignment
         });
      yPosition += template.footer.returnPolicy.fontSize + 5;
    }

    // Social media
    if (template.footer.socialMedia.show) {
      const socialInfo = [];
      if (template.footer.socialMedia.facebook) socialInfo.push(`FB: ${template.footer.socialMedia.facebook}`);
      if (template.footer.socialMedia.instagram) socialInfo.push(`IG: ${template.footer.socialMedia.instagram}`);
      if (template.footer.socialMedia.twitter) socialInfo.push(`TW: ${template.footer.socialMedia.twitter}`);
      
      if (socialInfo.length > 0) {
        doc.fontSize(template.footer.socialMedia.fontSize)
           .font('Helvetica')
           .text(socialInfo.join(' | '), {
             align: template.footer.socialMedia.alignment
           });
        yPosition += template.footer.socialMedia.fontSize + 5;
      }
    }

    // Custom footer
    if (template.footer.customFooter.show && template.footer.customFooter.text) {
      doc.fontSize(template.footer.customFooter.fontSize)
         .font('Helvetica')
         .text(template.footer.customFooter.text, {
           align: template.footer.customFooter.alignment
         });
      yPosition += template.footer.customFooter.fontSize + 10;
    }

    // Promotional message
    if (template.footer.promotionalMessage?.show && template.footer.promotionalMessage.text) {
      doc.fontSize(template.footer.promotionalMessage.fontSize)
         .fillColor(template.footer.promotionalMessage.textColor || '#000000')
         .text(template.footer.promotionalMessage.text, {
           align: template.footer.promotionalMessage.alignment
         });
      yPosition += template.footer.promotionalMessage.fontSize + 10;
    }

    // Dynamic promotional content
    const promotions = generatePromotionalContent(template, transactionData);
    if (promotions.length > 0) {
      promotions.forEach(promo => {
        doc.fontSize(10)
           .fillColor('#0066cc')
           .text(promo, { align: 'center' });
        yPosition += 15;
      });
    }

    // QR Code
    if (template.footer.qrCode?.show) {
      let qrData = template.footer.qrCode.url;

      // Generate QR code based on type
      switch (template.footer.qrCode.type) {
        case 'feedback':
          qrData = `${template.footer.qrCode.url || 'https://feedback.example.com'}?store=${storeId}&transaction=${transactionId}`;
          break;
        case 'loyalty':
          qrData = `${template.footer.qrCode.url || 'https://loyalty.example.com'}?store=${storeId}&points=${Math.floor(totalAmount)}`;
          break;
        case 'website':
          qrData = template.footer.qrCode.url || store.website || 'https://example.com';
          break;
        default:
          qrData = template.footer.qrCode.url || `https://receipt.example.com/${transactionId}`;
      }

      try {
        const qrCodeImage = await generateQRCode(qrData, template.footer.qrCode.size || 100);
        if (qrCodeImage) {
          // Convert data URL to buffer
          const base64Data = qrCodeImage.replace(/^data:image\/png;base64,/, '');
          const qrBuffer = Buffer.from(base64Data, 'base64');

          // Add QR code to PDF
          const qrSize = template.footer.qrCode.size || 100;
          const pageWidth = template.layout.paperWidth * 2.83;
          let qrX;

          switch (template.footer.qrCode.alignment) {
            case 'left':
              qrX = template.layout.margin * 2.83;
              break;
            case 'right':
              qrX = pageWidth - qrSize - (template.layout.margin * 2.83);
              break;
            default:
              qrX = (pageWidth - qrSize) / 2;
          }

          doc.image(qrBuffer, qrX, yPosition, { width: qrSize, height: qrSize });

          // Add QR code label
          if (template.footer.qrCode.label) {
            doc.fontSize(8)
               .fillColor('#000000')
               .text(template.footer.qrCode.label, qrX, yPosition + qrSize + 5, {
                 width: qrSize,
                 align: 'center'
               });
          }
        }
      } catch (qrError) {
        console.error('QR Code generation failed:', qrError);
      }
    }

    // Loyalty program information
    if (template.advanced?.loyaltyProgram?.enabled) {
      yPosition += 20;
      doc.fontSize(10)
         .fillColor('#000000')
         .text(`${template.advanced.loyaltyProgram.programName || 'Loyalty Program'}`, { align: 'center' });

      if (template.advanced.loyaltyProgram.pointsEarned) {
        const pointsEarned = Math.floor(totalAmount);
        doc.fontSize(8)
           .text(`Points earned: ${pointsEarned}`, { align: 'center' });
      }
    }

    doc.end();

    res.status(200).json({
      message: "Custom receipt generated successfully",
      receiptPath: `/receipts/${path.basename(receiptPath)}`
    });

  } catch (error) {
    console.error('Receipt generation error:', error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// Preview receipt template
exports.previewReceiptTemplate = async (req, res) => {
  try {
    const { storeId } = req.params;
    const templateData = req.body;

    // Validate storeId
    if (!storeId) {
      return res.status(400).json({ message: "Store ID is required" });
    }

    // Mock data for preview
    const mockProducts = [
      { name: 'Sample Product 1', price: 25.99, quantity: 2, barcode: '1234567890' },
      { name: 'Sample Product 2', price: 15.50, quantity: 1, barcode: '0987654321' }
    ];
    const mockTotal = 67.48;
    const mockTransactionId = 'PREVIEW-' + Date.now();

    // Generate preview receipt
    const previewData = {
      products: mockProducts,
      totalAmount: mockTotal,
      storeId: storeId,
      transactionId: mockTransactionId,
      systemId: 'PREVIEW-SYSTEM'
    };

    // Create a mock request object with the preview data and template
    const mockReq = {
      body: {
        ...previewData,
        template: templateData
      }
    };

    // Use the same generation logic but mark as preview
    await exports.generateCustomReceipt(mockReq, res);

  } catch (error) {
    console.error('Preview generation error:', error);
    res.status(500).json({ message: "Server error", error: error.message });
  }
};

// Reset receipt template to default
exports.resetReceiptTemplate = async (req, res) => {
  try {
    const { storeId } = req.params;

    // Check authorization
    if (req.user.role === "store" && req.user.storeId.toString() !== storeId) {
      return res.status(403).json({ message: "Access denied" });
    }

    const store = await Store.findById(storeId);
    if (!store) {
      return res.status(404).json({ message: "Store not found" });
    }

    // Reset to default template
    store.receiptTemplate = {};
    await store.save();

    res.status(200).json({
      message: "Receipt template reset to default successfully",
      template: store.receiptTemplate
    });

  } catch (error) {
    res.status(500).json({ message: "Server error", error: error.message });
  }
};
