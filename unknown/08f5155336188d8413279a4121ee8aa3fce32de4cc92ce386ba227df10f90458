{"version": 3, "sources": ["../src/index.js"], "names": ["tags", "require", "bytes", "SOIMarkerLength", "JPEGSOIMarker", "TIFFINTEL", "TIFFMOTOROLA", "APPMarker<PERSON>ength", "APPMarkerBegin", "APPMarkerEnd", "data", "<PERSON><PERSON><PERSON><PERSON>", "buffer", "S<PERSON><PERSON><PERSON><PERSON>", "readUInt16BE", "e", "Error", "isTiff", "checkAPPn", "APPMarkerTag", "isInRange", "IFDHandler", "tagCollection", "order", "offset", "entriesNumber", "readUInt16LE", "entriesNumberLength", "entries", "slice", "entry<PERSON><PERSON>th", "exif", "entryCount", "entryBegin", "entry", "tagBegin", "tag<PERSON><PERSON><PERSON>", "dataFormatBegin", "dataFormatLength", "componentsBegin", "componentsNumberLength", "dataValueBegin", "dataValueLength", "tag<PERSON><PERSON><PERSON>", "tagNumber", "toString", "reverse", "tagName", "bigDataFormat", "littleDataFormat", "dataFormat", "componentsByte", "bigComponentsNumber", "readUInt32BE", "littleComponentNumber", "readUInt32LE", "componentsNumber", "dataLength", "dataValue", "dataOffset", "tagValue", "readUInt8", "replace", "i", "length", "bigTagValue", "littleTagValue", "push", "bigOrder", "readInt32BE", "littleOrder", "readInt32LE", "EXIFHandler", "buf", "pad", "lengthLength", "identifierLength", "<PERSON><PERSON><PERSON><PERSON>", "byteOrderLength", "byteOrder", "fortyTwoLength", "fortyTwoEnd", "big42", "little42", "offsetOfIFD", "ifd", "ExifIF<PERSON>oint<PERSON>", "SubExif", "GPSInfoIFDPointer", "gps", "GPSInfo", "APPnHandler", "fromBuffer", "undefined", "sync", "file", "fs", "readFileSync", "async", "callback", "Promise", "resolve", "reject", "readFile", "err", "error", "then", "d", "catch", "exports", "parse", "parseSync"], "mappings": ";;AAAA;;;;;;AAEA,IAAMA,OAAOC,QAAQ,aAAR,CAAb;;AAEA;;;;;;;;;;;;;;AAcA,IAAMC,QAAQ,CAAC,CAAD,EAAI,CAAJ,EAAO,CAAP,EAAU,CAAV,EAAa,CAAb,EAAgB,CAAhB,EAAmB,CAAnB,EAAsB,CAAtB,EAAyB,CAAzB,EAA4B,CAA5B,EAA+B,CAA/B,EAAkC,CAAlC,EAAqC,CAArC,CAAd;AACA,IAAMC,kBAAkB,CAAxB;AACA,IAAMC,gBAAgB,MAAtB;AACA,IAAMC,YAAY,MAAlB;AACA,IAAMC,eAAe,MAArB;AACA,IAAMC,kBAAkB,CAAxB;AACA,IAAMC,iBAAiB,MAAvB;AACA,IAAMC,eAAe,MAArB;AACA,IAAIC,aAAJ;AACA;;;;;;;;AAQA,IAAMC,UAAU,SAAVA,OAAU,CAACC,MAAD,EAAY;AAC1B,MAAI;AACF,QAAMC,YAAYD,OAAOE,YAAP,CAAoB,CAApB,CAAlB;AACA,WAAOD,cAAcT,aAArB;AACD,GAHD,CAGE,OAAOW,CAAP,EAAU;AACV,UAAM,IAAIC,KAAJ,CAAU,wBAAV,CAAN;AACD;AACF,CAPD;AAQA;;;;;AAKA,IAAMC,SAAS,SAATA,MAAS,CAACL,MAAD,EAAY;AACzB,MAAI;AACF,QAAMC,YAAYD,OAAOE,YAAP,CAAoB,CAApB,CAAlB;AACA,WAAOD,cAAcR,SAAd,IAA2BQ,cAAcP,YAAhD;AACD,GAHD,CAGE,OAAOS,CAAP,EAAU;AACV,UAAM,IAAIC,KAAJ,CAAU,wBAAV,CAAN;AACD;AACF,CAPD;AAQA;;;;;;;;AAQA,IAAME,YAAY,SAAZA,SAAY,CAACN,MAAD,EAAY;AAC5B,MAAI;AACF,QAAMO,eAAeP,OAAOE,YAAP,CAAoB,CAApB,CAArB;AACA,QAAMM,YAAYD,gBAAgBX,cAAhB,IAAkCW,gBAAgBV,YAApE;AACA,WAAOW,YAAYD,eAAeX,cAA3B,GAA4C,KAAnD;AACD,GAJD,CAIE,OAAOO,CAAP,EAAU;AACV,UAAM,IAAIC,KAAJ,CAAU,kBAAV,CAAN;AACD;AACF,CARD;AASA;;;;;;;;;;;AAWA,IAAMK,aAAa,SAAbA,UAAa,CAACT,MAAD,EAASU,aAAT,EAAwBC,KAAxB,EAA+BC,MAA/B,EAA0C;AAC3D,MAAMC,gBAAgBF,QAAQX,OAAOE,YAAP,CAAoB,CAApB,CAAR,GAAiCF,OAAOc,YAAP,CAAoB,CAApB,CAAvD;;AAEA,MAAID,kBAAkB,CAAtB,EAAyB;AACvB,WAAO,EAAP;AACD;;AAED,MAAME,sBAAsB,CAA5B;AACA,MAAMC,UAAUhB,OAAOiB,KAAP,CAAaF,mBAAb,CAAhB;AACA,MAAMG,cAAc,EAApB;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAO,EAAb;AACA,MAAIC,aAAa,CAAjB;;AAEA,OAAKA,UAAL,EAAiBA,aAAaP,aAA9B,EAA6CO,cAAc,CAA3D,EAA8D;AAC5D,QAAMC,aAAaD,aAAaF,WAAhC;AACA,QAAMI,QAAQN,QAAQC,KAAR,CAAcI,UAAd,EAA0BA,aAAaH,WAAvC,CAAd;AACA,QAAMK,WAAW,CAAjB;AACA,QAAMC,YAAY,CAAlB;AACA,QAAMC,kBAAkBF,WAAWC,SAAnC;AACA,QAAME,mBAAmB,CAAzB;AACA,QAAMC,kBAAkBF,kBAAkBC,gBAA1C;AACA,QAAME,yBAAyB,CAA/B;AACA,QAAMC,iBAAiBF,kBAAkBC,sBAAzC;AACA,QAAME,kBAAkB,CAAxB;AACA,QAAMC,aAAaT,MAAML,KAAN,CAAYM,QAAZ,EAAsBE,eAAtB,CAAnB;AACA,QAAMO,YAAYrB,QAAQoB,WAAWE,QAAX,CAAoB,KAApB,CAAR,GAAqCF,WAAWG,OAAX,GAAqBD,QAArB,CAA8B,KAA9B,CAAvD;AACA,QAAME,UAAUzB,cAAcsB,SAAd,CAAhB;AACA,QAAMI,gBAAgBd,MAAMpB,YAAN,CAAmBuB,eAAnB,CAAtB;AACA,QAAMY,mBAAmBf,MAAMR,YAAN,CAAmBW,eAAnB,CAAzB;AACA,QAAMa,aAAa3B,QAAQyB,aAAR,GAAwBC,gBAA3C;AACA,QAAME,iBAAiBjD,MAAMgD,UAAN,CAAvB;AACA,QAAME,sBAAsBlB,MAAMmB,YAAN,CAAmBd,eAAnB,CAA5B;AACA,QAAMe,wBAAwBpB,MAAMqB,YAAN,CAAmBhB,eAAnB,CAA9B;AACA,QAAMiB,mBAAmBjC,QAAQ6B,mBAAR,GAA8BE,qBAAvD;AACA,QAAMG,aAAaD,mBAAmBL,cAAtC;AACA,QAAIO,YAAYxB,MAAML,KAAN,CAAYY,cAAZ,EAA4BA,iBAAiBC,eAA7C,CAAhB;;AAEA,QAAIe,aAAa,CAAjB,EAAoB;AAClB,UAAME,aAAa,CAACpC,QAAQmC,UAAUL,YAAV,CAAuB,CAAvB,CAAR,GAAoCK,UAAUH,YAAV,CAAuB,CAAvB,CAArC,IAAkE/B,MAArF;AACAkC,kBAAY9C,OAAOiB,KAAP,CAAa8B,UAAb,EAAyBA,aAAaF,UAAtC,CAAZ;AACD;;AAED,QAAIG,iBAAJ;;AAEA,QAAIb,OAAJ,EAAa;AACX,cAAQG,UAAR;AACE,aAAK,CAAL;AACEU,qBAAWF,UAAUG,SAAV,CAAoB,CAApB,CAAX;AACA;AACF,aAAK,CAAL;AACED,qBAAWF,UAAUb,QAAV,CAAmB,OAAnB,EAA4BiB,OAA5B,CAAoC,MAApC,EAA4C,EAA5C,CAAX;AACA;AACF,aAAK,CAAL;AACEF,qBAAWrC,QAAQmC,UAAU5C,YAAV,CAAuB,CAAvB,CAAR,GAAoC4C,UAAUhC,YAAV,CAAuB,CAAvB,CAA/C;AACA;AACF,aAAK,CAAL;AACEkC,qBAAWrC,QAAQmC,UAAUL,YAAV,CAAuB,CAAvB,CAAR,GAAoCK,UAAUH,YAAV,CAAuB,CAAvB,CAA/C;AACA;AACF,aAAK,CAAL;AACEK,qBAAW,EAAX;;AAEA,eAAK,IAAIG,IAAI,CAAb,EAAgBA,IAAIL,UAAUM,MAA9B,EAAsCD,KAAK,CAA3C,EAA8C;AAC5C,gBAAME,cAAcP,UAAUL,YAAV,CAAuBU,CAAvB,IAA4BL,UAAUL,YAAV,CAAuBU,IAAI,CAA3B,CAAhD;AACA,gBAAMG,iBAAiBR,UAAUH,YAAV,CAAuBQ,CAAvB,IAA4BL,UAAUH,YAAV,CAAuBQ,IAAI,CAA3B,CAAnD;AACAH,qBAASO,IAAT,CAAc5C,QAAQ0C,WAAR,GAAsBC,cAApC;AACD;;AAED;AACF,aAAK,CAAL;AACE,kBAAQnB,OAAR;AACE,iBAAK,aAAL;AACEa,yBAAWF,UAAUb,QAAV,EAAX;AACA;AACF,iBAAK,iBAAL;AACEe,yBAAWF,UAAUb,QAAV,EAAX;AACA;AACF,iBAAK,WAAL;AACEe,yBAAWF,UAAUG,SAAV,CAAoB,CAApB,CAAX;AACA;AACF;AACED,gCAAgBF,UAAUb,QAAV,CAAmB,KAAnB,EAA0B,CAA1B,EAA6B,EAA7B,CAAhB;AACA;AAZJ;AAcA;AACF,aAAK,EAAL;AAAS;AACP,gBAAMuB,WAAWV,UAAUW,WAAV,CAAsB,CAAtB,IAA2BX,UAAUW,WAAV,CAAsB,CAAtB,CAA5C;AACA,gBAAMC,cAAcZ,UAAUa,WAAV,CAAsB,CAAtB,IAA2Bb,UAAUa,WAAV,CAAsB,CAAtB,CAA/C;AACAX,uBAAWrC,QAAQ6C,QAAR,GAAmBE,WAA9B;AACA;AACD;AACD;AACEV,4BAAgBF,UAAUb,QAAV,CAAmB,KAAnB,CAAhB;AACA;AA/CJ;AAiDAd,WAAKgB,OAAL,IAAgBa,QAAhB;AACD;AACD;;;;;AAKD;AACD,SAAO7B,IAAP;AACD,CA3GD;;AA6GA;;;;;;;AAOA,IAAMyC,cAAc,SAAdA,WAAc,CAACC,GAAD,EAAqB;AAAA,MAAfC,GAAe,uEAAT,IAAS;;AACvC,MAAI9D,SAAS6D,GAAb;;AAEA,MAAIC,GAAJ,EAAS;AACP9D,aAAS6D,IAAI5C,KAAJ,CAAUtB,eAAV,CAAT;AACA,QAAMyD,SAASpD,OAAOE,YAAP,CAAoB,CAApB,CAAf;AACAF,aAASA,OAAOiB,KAAP,CAAa,CAAb,EAAgBmC,MAAhB,CAAT;AACA,QAAMW,eAAe,CAArB;AACA/D,aAASA,OAAOiB,KAAP,CAAa8C,YAAb,CAAT;AACA,QAAMC,mBAAmB,CAAzB;AACAhE,aAASA,OAAOiB,KAAP,CAAa+C,gBAAb,CAAT;AACA,QAAMC,YAAY,CAAlB;AACAjE,aAASA,OAAOiB,KAAP,CAAagD,SAAb,CAAT;AACD;;AAED,MAAMC,kBAAkB,CAAxB;AACA,MAAMC,YAAYnE,OAAOiC,QAAP,CAAgB,OAAhB,EAAyB,CAAzB,EAA4BiC,eAA5B,MAAiD,IAAnE;AACA,MAAME,iBAAiB,CAAvB;AACA,MAAMC,cAAcH,kBAAkBE,cAAtC;AACA,MAAME,QAAQtE,OAAOyC,YAAP,CAAoB4B,WAApB,CAAd;AACA,MAAME,WAAWvE,OAAO2C,YAAP,CAAoB0B,WAApB,CAAjB;AACA,MAAMG,cAAcL,YAAYG,KAAZ,GAAoBC,QAAxC;;AAEAvE,WAASA,OAAOiB,KAAP,CAAauD,WAAb,CAAT;;AAEA,MAAIxE,OAAOoD,MAAP,GAAgB,CAApB,EAAuB;AACrBtD,WAAOW,WAAWT,MAAX,EAAmBZ,KAAKqF,GAAxB,EAA6BN,SAA7B,EAAwCK,WAAxC,CAAP;;AAEA,QAAI1E,KAAK4E,cAAT,EAAyB;AACvB1E,eAASA,OAAOiB,KAAP,CAAanB,KAAK4E,cAAL,GAAsBF,WAAnC,CAAT;AACA1E,WAAK6E,OAAL,GAAelE,WAAWT,MAAX,EAAmBZ,KAAKqF,GAAxB,EAA6BN,SAA7B,EAAwCrE,KAAK4E,cAA7C,CAAf;AACD;;AAED,QAAI5E,KAAK8E,iBAAT,EAA4B;AAC1B,UAAMC,MAAM/E,KAAK8E,iBAAjB;AACA5E,eAASA,OAAOiB,KAAP,CAAanB,KAAK4E,cAAL,GAAsBG,MAAM/E,KAAK4E,cAAjC,GAAkDG,MAAML,WAArE,CAAT;AACA1E,WAAKgF,OAAL,GAAerE,WAAWT,MAAX,EAAmBZ,KAAKyF,GAAxB,EAA6BV,SAA7B,EAAwCU,GAAxC,CAAf;AACD;AACF;AACF,CAvCD;;AAyCA;;;;;;;AAOA,IAAME,cAAc,SAAdA,WAAc,CAAC/E,MAAD,EAAY;AAC9B,MAAMO,eAAeD,UAAUN,MAAV,CAArB;;AAEA,MAAIO,iBAAiB,KAArB,EAA4B;AAAE;AAC5B,QAAM6C,SAASpD,OAAOE,YAAP,CAAoBP,eAApB,CAAf;;AAEA,YAAQY,YAAR;AACE,WAAK,CAAL;AAAQ;AACNqD,oBAAY5D,MAAZ;AACA;AACF;AACE+E,oBAAY/E,OAAOiB,KAAP,CAAatB,kBAAkByD,MAA/B,CAAZ;AACA;AANJ;AAQD;AACF,CAfD;;AAiBA;;;;;AAKA,IAAM4B,aAAa,SAAbA,UAAa,CAAChF,MAAD,EAAY;AAC7B,MAAI,CAACA,MAAL,EAAa;AACX,UAAM,IAAII,KAAJ,CAAU,kBAAV,CAAN;AACD;;AAEDN,SAAOmF,SAAP;;AAEA,MAAIlF,QAAQC,MAAR,CAAJ,EAAqB;AACnBA,aAASA,OAAOiB,KAAP,CAAa1B,eAAb,CAAT;AACAO,WAAO,EAAP;AACAiF,gBAAY/E,MAAZ;AACD,GAJD,MAIO,IAAIK,OAAOL,MAAP,CAAJ,EAAoB;AACzBF,WAAO,EAAP;AACA8D,gBAAY5D,MAAZ,EAAoB,KAApB;AACD;;AAED,SAAOF,IAAP;AACD,CAjBD;;AAmBA;;;;;;;AAOA,IAAMoF,OAAO,SAAPA,IAAO,CAACC,IAAD,EAAU;AACrB,MAAI,CAACA,IAAL,EAAW;AACT,UAAM,IAAI/E,KAAJ,CAAU,gBAAV,CAAN;AACD;;AAED,MAAMJ,SAASoF,aAAGC,YAAH,CAAgBF,IAAhB,CAAf;;AAEA,SAAOH,WAAWhF,MAAX,CAAP;AACD,CARD;;AAUA;;;;;;;;;;;;;AAaA,IAAMsF,QAAQ,SAARA,KAAQ,CAACH,IAAD,EAAOI,QAAP,EAAoB;AAChCzF,SAAOmF,SAAP;;AAEA,MAAIO,OAAJ,CAAY,UAACC,OAAD,EAAUC,MAAV,EAAqB;AAC/B,QAAI,CAACP,IAAL,EAAW;AACTO,aAAO,IAAItF,KAAJ,CAAU,kBAAV,CAAP;AACD;;AAEDgF,iBAAGO,QAAH,CAAYR,IAAZ,EAAkB,UAACS,GAAD,EAAM5F,MAAN,EAAiB;AACjC,UAAI4F,GAAJ,EAAS;AACPF,eAAOE,GAAP;AACD,OAFD,MAEO;AACL,YAAI;AACF,cAAI7F,QAAQC,MAAR,CAAJ,EAAqB;AACnB,gBAAM6D,MAAM7D,OAAOiB,KAAP,CAAa1B,eAAb,CAAZ;;AAEAO,mBAAO,EAAP;;AAEAiF,wBAAYlB,GAAZ;AACA4B,oBAAQ3F,IAAR;AACD,WAPD,MAOO,IAAIO,OAAOL,MAAP,CAAJ,EAAoB;AACzBF,mBAAO,EAAP;;AAEA8D,wBAAY5D,MAAZ,EAAoB,KAApB;AACAyF,oBAAQ3F,IAAR;AACD,WALM,MAKA;AACL4F,mBAAO,IAAItF,KAAJ,CAAU,wBAAV,CAAP;AACD;AACF,SAhBD,CAgBE,OAAOD,CAAP,EAAU;AACVuF,iBAAOvF,CAAP;AACD;AACF;AACF,KAxBD;AAyBD,GA9BD,EA8BG,UAAC0F,KAAD,EAAW;AACZN,aAASM,KAAT,EAAgBZ,SAAhB;AACD,GAhCD,EAgCGa,IAhCH,CAgCQ,UAACC,CAAD,EAAO;AACbR,aAASN,SAAT,EAAoBc,CAApB;AACD,GAlCD,EAkCGC,KAlCH,CAkCS,UAACH,KAAD,EAAW;AAClBN,aAASM,KAAT,EAAgBZ,SAAhB;AACD,GApCD;AAqCD,CAxCD;;AA0CAgB,QAAQjB,UAAR,GAAqBA,UAArB;AACAiB,QAAQC,KAAR,GAAgBZ,KAAhB;AACAW,QAAQE,SAAR,GAAoBjB,IAApB", "file": "index.js", "sourcesContent": ["import fs from 'fs';\n\nconst tags = require('./tags.json');\n\n/*\n unsignedByte,\n asciiStrings,\n unsignedShort,\n unsignedLong,\n unsignedRational,\n signedByte,\n undefined,\n signedShort,\n signedLong,\n signedRational,\n singleFloat,\n doubleFloat\n */\nconst bytes = [0, 1, 1, 2, 4, 8, 1, 1, 2, 4, 8, 4, 8];\nconst SOIMarkerLength = 2;\nconst JPEGSOIMarker = 0xffd8;\nconst TIFFINTEL = 0x4949;\nconst TIFFMOTOROLA = 0x4d4d;\nconst APPMarkerLength = 2;\nconst APPMarkerBegin = 0xffe0;\nconst APPMarkerEnd = 0xffef;\nlet data;\n/**\n * @param buffer {Buffer}\n * @returns {Boolean}\n * @example\n * var content = fs.readFileSync(\"~/Picture/IMG_0911.JPG\");\n * var isImage = isValid(content);\n * console.log(isImage);\n */\nconst isValid = (buffer) => {\n  try {\n    const SOIMarker = buffer.readUInt16BE(0);\n    return SOIMarker === JPEGSOIMarker;\n  } catch (e) {\n    throw new Error('Unsupport file format.');\n  }\n};\n/**\n * @param buffer {Buffer}\n * @returns {Boolean}\n * @example\n */\nconst isTiff = (buffer) => {\n  try {\n    const SOIMarker = buffer.readUInt16BE(0);\n    return SOIMarker === TIFFINTEL || SOIMarker === TIFFMOTOROLA;\n  } catch (e) {\n    throw new Error('Unsupport file format.');\n  }\n};\n/**\n * @param buffer {Buffer}\n * @returns {Number}\n * @example\n * var content = fs.readFileSync(\"~/Picture/IMG_0911.JPG\");\n * var APPNumber = checkAPPn(content);\n * console.log(APPNumber);\n */\nconst checkAPPn = (buffer) => {\n  try {\n    const APPMarkerTag = buffer.readUInt16BE(0);\n    const isInRange = APPMarkerTag >= APPMarkerBegin && APPMarkerTag <= APPMarkerEnd;\n    return isInRange ? APPMarkerTag - APPMarkerBegin : false;\n  } catch (e) {\n    throw new Error('Invalid APP Tag.');\n  }\n};\n/**\n * @param buffer {Buffer}\n * @param tagCollection {Object}\n * @param order {Boolean}\n * @param offset {Number}\n * @returns {Object}\n * @example\n * var content = fs.readFileSync(\"~/Picture/IMG_0911.JPG\");\n * var exifFragments = IFDHandler(content, 0, true, 8);\n * console.log(exifFragments.value);\n */\nconst IFDHandler = (buffer, tagCollection, order, offset) => {\n  const entriesNumber = order ? buffer.readUInt16BE(0) : buffer.readUInt16LE(0);\n\n  if (entriesNumber === 0) {\n    return {};\n  }\n\n  const entriesNumberLength = 2;\n  const entries = buffer.slice(entriesNumberLength);\n  const entryLength = 12;\n  // let nextIFDPointerBegin = entriesNumberLength + entryLength * entriesNumber;\n  // let bigNextIFDPointer= buffer.readUInt32BE(nextIFDPointerBegin) ;\n  // let littleNextIFDPointer= buffer.readUInt32LE(nextIFDPointerBegin);\n  // let nextIFDPointer = order ?bigNextIFDPointer:littleNextIFDPointer;\n  const exif = {};\n  let entryCount = 0;\n\n  for (entryCount; entryCount < entriesNumber; entryCount += 1) {\n    const entryBegin = entryCount * entryLength;\n    const entry = entries.slice(entryBegin, entryBegin + entryLength);\n    const tagBegin = 0;\n    const tagLength = 2;\n    const dataFormatBegin = tagBegin + tagLength;\n    const dataFormatLength = 2;\n    const componentsBegin = dataFormatBegin + dataFormatLength;\n    const componentsNumberLength = 4;\n    const dataValueBegin = componentsBegin + componentsNumberLength;\n    const dataValueLength = 4;\n    const tagAddress = entry.slice(tagBegin, dataFormatBegin);\n    const tagNumber = order ? tagAddress.toString('hex') : tagAddress.reverse().toString('hex');\n    const tagName = tagCollection[tagNumber];\n    const bigDataFormat = entry.readUInt16BE(dataFormatBegin);\n    const littleDataFormat = entry.readUInt16LE(dataFormatBegin);\n    const dataFormat = order ? bigDataFormat : littleDataFormat;\n    const componentsByte = bytes[dataFormat];\n    const bigComponentsNumber = entry.readUInt32BE(componentsBegin);\n    const littleComponentNumber = entry.readUInt32LE(componentsBegin);\n    const componentsNumber = order ? bigComponentsNumber : littleComponentNumber;\n    const dataLength = componentsNumber * componentsByte;\n    let dataValue = entry.slice(dataValueBegin, dataValueBegin + dataValueLength);\n\n    if (dataLength > 4) {\n      const dataOffset = (order ? dataValue.readUInt32BE(0) : dataValue.readUInt32LE(0)) - offset;\n      dataValue = buffer.slice(dataOffset, dataOffset + dataLength);\n    }\n\n    let tagValue;\n\n    if (tagName) {\n      switch (dataFormat) {\n        case 1:\n          tagValue = dataValue.readUInt8(0);\n          break;\n        case 2:\n          tagValue = dataValue.toString('ascii').replace(/\\0+$/, '');\n          break;\n        case 3:\n          tagValue = order ? dataValue.readUInt16BE(0) : dataValue.readUInt16LE(0);\n          break;\n        case 4:\n          tagValue = order ? dataValue.readUInt32BE(0) : dataValue.readUInt32LE(0);\n          break;\n        case 5:\n          tagValue = [];\n\n          for (let i = 0; i < dataValue.length; i += 8) {\n            const bigTagValue = dataValue.readUInt32BE(i) / dataValue.readUInt32BE(i + 4);\n            const littleTagValue = dataValue.readUInt32LE(i) / dataValue.readUInt32LE(i + 4);\n            tagValue.push(order ? bigTagValue : littleTagValue);\n          }\n\n          break;\n        case 7:\n          switch (tagName) {\n            case 'ExifVersion':\n              tagValue = dataValue.toString();\n              break;\n            case 'FlashPixVersion':\n              tagValue = dataValue.toString();\n              break;\n            case 'SceneType':\n              tagValue = dataValue.readUInt8(0);\n              break;\n            default:\n              tagValue = `0x${dataValue.toString('hex', 0, 15)}`;\n              break;\n          }\n          break;\n        case 10: {\n          const bigOrder = dataValue.readInt32BE(0) / dataValue.readInt32BE(4);\n          const littleOrder = dataValue.readInt32LE(0) / dataValue.readInt32LE(4);\n          tagValue = order ? bigOrder : littleOrder;\n          break;\n        }\n        default:\n          tagValue = `0x${dataValue.toString('hex')}`;\n          break;\n      }\n      exif[tagName] = tagValue;\n    }\n    /*\n     else {\n     console.log(`Unkown Tag [0x${tagNumber}].`);\n     }\n     */\n  }\n  return exif;\n};\n\n/**\n * @param buf {Buffer}\n * @returns {Undefined}\n * @example\n * var content = fs.readFileSync(\"~/Picture/IMG_0911.JPG\");\n * var exifFragments = EXIFHandler(content);\n */\nconst EXIFHandler = (buf, pad = true) => {\n  let buffer = buf;\n\n  if (pad) {\n    buffer = buf.slice(APPMarkerLength);\n    const length = buffer.readUInt16BE(0);\n    buffer = buffer.slice(0, length);\n    const lengthLength = 2;\n    buffer = buffer.slice(lengthLength);\n    const identifierLength = 5;\n    buffer = buffer.slice(identifierLength);\n    const padLength = 1;\n    buffer = buffer.slice(padLength);\n  }\n\n  const byteOrderLength = 2;\n  const byteOrder = buffer.toString('ascii', 0, byteOrderLength) === 'MM';\n  const fortyTwoLength = 2;\n  const fortyTwoEnd = byteOrderLength + fortyTwoLength;\n  const big42 = buffer.readUInt32BE(fortyTwoEnd);\n  const little42 = buffer.readUInt32LE(fortyTwoEnd);\n  const offsetOfIFD = byteOrder ? big42 : little42;\n\n  buffer = buffer.slice(offsetOfIFD);\n\n  if (buffer.length > 0) {\n    data = IFDHandler(buffer, tags.ifd, byteOrder, offsetOfIFD);\n\n    if (data.ExifIFDPointer) {\n      buffer = buffer.slice(data.ExifIFDPointer - offsetOfIFD);\n      data.SubExif = IFDHandler(buffer, tags.ifd, byteOrder, data.ExifIFDPointer);\n    }\n\n    if (data.GPSInfoIFDPointer) {\n      const gps = data.GPSInfoIFDPointer;\n      buffer = buffer.slice(data.ExifIFDPointer ? gps - data.ExifIFDPointer : gps - offsetOfIFD);\n      data.GPSInfo = IFDHandler(buffer, tags.gps, byteOrder, gps);\n    }\n  }\n};\n\n/**\n * @param buffer {Buffer}\n * @returns {Undefined}\n * @example\n * var content = fs.readFileSync(\"~/Picture/IMG_0911.JPG\");\n * var exifFragments = APPnHandler(content);\n */\nconst APPnHandler = (buffer) => {\n  const APPMarkerTag = checkAPPn(buffer);\n\n  if (APPMarkerTag !== false) { // APP0 is 0, and 0==false\n    const length = buffer.readUInt16BE(APPMarkerLength);\n\n    switch (APPMarkerTag) {\n      case 1: // EXIF\n        EXIFHandler(buffer);\n        break;\n      default:\n        APPnHandler(buffer.slice(APPMarkerLength + length));\n        break;\n    }\n  }\n};\n\n/**\n * @param buffer {Buffer}\n * @returns {Object}\n * @example\n */\nconst fromBuffer = (buffer) => {\n  if (!buffer) {\n    throw new Error('buffer not found');\n  }\n\n  data = undefined;\n\n  if (isValid(buffer)) {\n    buffer = buffer.slice(SOIMarkerLength);\n    data = {};\n    APPnHandler(buffer);\n  } else if (isTiff(buffer)) {\n    data = {};\n    EXIFHandler(buffer, false);\n  }\n\n  return data;\n};\n\n/**\n * @param file {String}\n * @returns {Object}\n * @example\n * var exif = sync(\"~/Picture/IMG_1981.JPG\");\n * console.log(exif.createTime);\n */\nconst sync = (file) => {\n  if (!file) {\n    throw new Error('File not found');\n  }\n\n  const buffer = fs.readFileSync(file);\n\n  return fromBuffer(buffer);\n};\n\n/**\n * @param file {String}\n * @param callback {Function}\n * @example\n * async(\"~/Picture/IMG_0707.JPG\", (err, data) => {\n *     if(err) {\n *         console.log(err);\n *     }\n *     if(data) {\n *         console.log(data.ExifOffset.createTime);\n *     }\n * }\n */\nconst async = (file, callback) => {\n  data = undefined;\n\n  new Promise((resolve, reject) => {\n    if (!file) {\n      reject(new Error('❓File not found.'));\n    }\n\n    fs.readFile(file, (err, buffer) => {\n      if (err) {\n        reject(err);\n      } else {\n        try {\n          if (isValid(buffer)) {\n            const buf = buffer.slice(SOIMarkerLength);\n\n            data = {};\n\n            APPnHandler(buf);\n            resolve(data);\n          } else if (isTiff(buffer)) {\n            data = {};\n\n            EXIFHandler(buffer, false);\n            resolve(data);\n          } else {\n            reject(new Error('😱Unsupport file type.'));\n          }\n        } catch (e) {\n          reject(e);\n        }\n      }\n    });\n  }, (error) => {\n    callback(error, undefined);\n  }).then((d) => {\n    callback(undefined, d);\n  }).catch((error) => {\n    callback(error, undefined);\n  });\n};\n\nexports.fromBuffer = fromBuffer;\nexports.parse = async;\nexports.parseSync = sync;\n"]}